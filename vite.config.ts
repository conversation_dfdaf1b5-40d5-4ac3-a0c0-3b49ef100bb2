import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";

export default defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...(process.env.NODE_ENV !== "production" &&
    process.env.REPL_ID !== undefined
      ? [
          await import("@replit/vite-plugin-cartographer").then((m) =>
            m.cartographer(),
          ),
        ]
      : []),
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets"),
    },
  },
  root: path.resolve(import.meta.dirname, "client"),
  base: "/",
  build: {
    outDir: "../dist",
    emptyOutDir: true,
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunks
          if (id.includes('node_modules')) {
            // Stripe - separate chunk (lazy loaded)
            if (id.includes('@stripe')) {
              return 'stripe-vendor';
            }
            // Analytics - separate chunk
            if (id.includes('@vercel/analytics') || id.includes('@vercel/speed-insights')) {
              return 'analytics-vendor';
            }
            // React core
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-vendor';
            }
            // UI libraries
            if (id.includes('framer-motion') || id.includes('lucide-react') || id.includes('@radix-ui')) {
              return 'ui-vendor';
            }
            // Data fetching
            if (id.includes('@tanstack/react-query')) {
              return 'query-vendor';
            }
            // Supabase
            if (id.includes('@supabase')) {
              return 'supabase-vendor';
            }
            // Router
            if (id.includes('wouter')) {
              return 'router-vendor';
            }
            // Other vendor libraries
            return 'vendor';
          }

          // Page chunks
          if (id.includes('/pages/dashboard') || id.includes('/pages/idea-detail')) {
            return 'dashboard-pages';
          }
          if (id.includes('/pages/faq') || id.includes('/pages/blog')) {
            return 'content-pages';
          }
          if (id.includes('/pages/industry-overview') || id.includes('/pages/best-ideas')) {
            return 'industry-pages';
          }
          if (id.includes('/pages/admin') || id.includes('/pages/auth-callback')) {
            return 'admin-pages';
          }

          // Performance monitoring - separate chunk
          if (id.includes('performance-monitor') || id.includes('performance-dashboard')) {
            return 'performance-vendor';
          }
        }
      }
    },
    // Performance optimizations
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
        passes: 2,
      },
      mangle: {
        safari10: true,
      },
    },
    // Tree shaking
    treeshake: {
      preset: 'recommended',
      moduleSideEffects: false,
    },
    // Chunk size warnings
    chunkSizeWarningLimit: 500,
    // CSS code splitting
    cssCodeSplit: true,
  },
  server: {
    fs: {
      strict: true,
      deny: ["**/.*"],
    },
  },
});
