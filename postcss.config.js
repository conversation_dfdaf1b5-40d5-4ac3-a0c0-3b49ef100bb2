export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
    ...(process.env.NODE_ENV === 'production' && {
      '@fullhuman/postcss-purgecss': {
        content: ['./client/index.html', './client/src/**/*.{js,jsx,ts,tsx}'],
        defaultExtractor: content => content.match(/[\w-/:]+(?<!:)/g) || [],
        safelist: {
          standard: [/^glass-/, /^neon-/, /^particle-/, /^gpu-/, /^stable-/],
          deep: [/^bg-/, /^text-/, /^border-/],
          greedy: [/^hover:/, /^focus:/, /^active:/]
        }
      }
    })
  },
}
